/* Custom scrollbar for a more Notion-like feel (optional) */
::-webkit-scrollbar { width: 8px; height: 8px; }
::-webkit-scrollbar-track { background: transparent; }
::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 4px; }
.dark ::-webkit-scrollbar-thumb { background: #475569; }
::-webkit-scrollbar-thumb:hover { background: #94a3b8; }
.dark ::-webkit-scrollbar-thumb:hover { background: #64748b; }

/* Style for active tool button */
.active-tool {
 background-color: theme('colors.indigo.100');
 color: theme('colors.indigo.700');
}
.dark .active-tool {
 background-color: theme('colors.indigo.700');
 color: theme('colors.indigo.200');
}
/* Ensure canvas selection box is visible in dark mode */
.dark .canvas-container .upper-canvas .selection {
 border-color: rgba(165, 180, 252, 0.7) !important; /* Tailwind indigo-300 with alpha */
 background-color: rgba(129, 140, 248, 0.2) !important; /* Tailwind indigo-400 with alpha */
}
#magnifier-preview {
 backdrop-filter: blur(4px);
 -webkit-backdrop-filter: blur(4px);
}
